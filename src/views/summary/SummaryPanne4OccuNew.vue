<template>
  <div style="padding: 5px">
    <a-drawer title="登记列表" v-model:open="visible" width="60%" :after-close="afterClose" placement="left">
      <customer-reg-list4-summary4-occu ref="customerRegList" @row-click="handleRegTableRowClick" />
    </a-drawer>

    <a-row :gutter="4">
      <a-col :span="24">
        <a-alert v-if="criticalItems.length > 0" :message="criticalTip" type="error" show-icon style="margin-bottom: 6px" />
        <a-alert
          v-if="showAuditTip"
          :message="'总检驳回：' + auditRecord.rejectReason"
          type="error"
          show-icon
          style="margin-bottom: 6px"
          :closable="true"
        />

        <!-- 检索栏和用户卡片合并在一行 -->
        <div style="display: flex; gap: 4px; margin-bottom: 6px; align-items: center">
          <!-- 左侧检索栏卡片 -->
          <div style="flex: 1; width: 50%">
            <a-card
              size="small"
              style="padding: 6px 10px; background-color: #fff; border: 1px solid #e8e8e8; height: 56px; display: flex; align-items: center"
            >
              <div style="display: flex; align-items: center; gap: 8px; width: 100%">
                <!-- 登记列表按钮 -->
                <a-button
                  type="primary"
                  size="small"
                  @click="openDrawer"
                  style="
                    border-radius: 12px;
                    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
                    height: 26px;
                    padding: 0 8px;
                    font-size: 12px;
                    flex-shrink: 0;
                  "
                >
                  <template #icon>
                    <UnorderedListOutlined style="font-size: 12px" />
                  </template>
                  登记列表
                </a-button>

                <!-- 体检号输入框 -->
                <div style="display: flex; align-items: center; gap: 4px; flex-shrink: 0">
                  <span style="color: #666; font-size: 12px; white-space: nowrap">体检号</span>
                  <a-input
                    v-model:value="queryParam.examNo"
                    placeholder="体检号搜索"
                    size="small"
                    allow-clear
                    style="height: 26px; width: 130px; font-size: 12px"
                    @keyup.enter="searchQuery"
                  />
                </div>

                <!-- 姓名输入框 -->
                <div style="display: flex; align-items: center; gap: 4px; flex-shrink: 0">
                  <span style="color: #666; font-size: 12px; white-space: nowrap">姓名</span>
                  <a-input
                    v-model:value="queryParam.name"
                    placeholder="姓名"
                    size="small"
                    allow-clear
                    style="height: 26px; width: 100px; font-size: 12px"
                    @keyup.enter="searchQuery"
                  />
                </div>

                <!-- 证件号输入框 -->
                <div style="display: flex; align-items: center; gap: 4px; flex-shrink: 0">
                  <span style="color: #666; font-size: 12px; white-space: nowrap">证件号</span>
                  <a-input
                    v-model:value="queryParam.idCard"
                    placeholder="证件号"
                    size="small"
                    allow-clear
                    style="height: 26px; width: 120px; font-size: 12px"
                    @keyup.enter="searchQuery"
                  />
                </div>

                <!-- 操作按钮 -->
                <div style="display: flex; gap: 6px; margin-left: auto">
                  <a-button size="small" type="primary" @click="searchQuery" style="height: 26px; padding: 0 8px; font-size: 12px">
                    <template #icon>
                      <SearchOutlined style="font-size: 12px" />
                    </template>
                    查询
                  </a-button>
                  <a-button size="small" @click="searchReset" style="height: 26px; padding: 0 8px; font-size: 12px">
                    <template #icon>
                      <ReloadOutlined style="font-size: 12px" />
                    </template>
                    重置
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>

          <!-- 右侧用户卡片 -->
          <div style="flex-shrink: 0; width: 50%">
            <!-- 有用户数据时显示 -->
            <div v-if="currentReg?.id">
              <a-card
                size="small"
                style="padding: 6px 10px; background-color: #fff; border: 1px solid #e8e8e8; height: 56px; display: flex; align-items: center"
              >
                <!-- 用户信息主体 -->
                <div style="display: flex; justify-content: space-between; align-items: center; gap: 10px; width: 48vw; height: 100%">
                  <!-- 左侧信息 -->
                  <div style="width: 70%; min-width: 0; display: flex; flex-direction: column; justify-content: center; height: 100%">
                    <!-- 第一行：姓名、性别、年龄、体检号、状态 -->
                    <div style="display: flex; align-items: baseline; gap: 8px; margin-bottom: 4px">
                      <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: #262626; line-height: 1.2; flex-shrink: 0">
                        {{ currentReg.name || '' }}
                      </h3>
                      <span style="color: #8c8c8c; font-size: 12px; flex-shrink: 0">{{ currentReg.gender || '' }}</span>
                      <span style="color: #8c8c8c; font-size: 12px; flex-shrink: 0">
                        {{ currentReg.age ? currentReg.age + currentReg.ageUnit : '' }}
                      </span>
                      <!-- 体检号和状态 -->
                      <span
                        style="
                          font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
                          font-size: 13px;
                          font-weight: 500;
                          color: #1890ff;
                          letter-spacing: 0.5px;
                          margin-left: 8px;
                          flex-shrink: 0;
                        "
                      >
                        {{ currentReg.examNo || '' }}
                      </span>

                      <span style="font-size: 12px; font-weight: 500; color: #52c41a">
                        {{ customerSummary.status || '未总检' }}
                      </span>
                    </div>

                    <!-- 第二行：医生信息和详细信息 -->
                    <div style="display: flex; align-items: center; gap: 8px; flex-wrap: nowrap; width: 100%; overflow: hidden">
                      <!-- 医生信息 -->
                      <div
                        v-if="customerSummary.initailDoctor || customerSummary.chiefDoctor || customerSummary.auditor"
                        style="display: flex; align-items: center; gap: 4px; flex-shrink: 0"
                      >
                        <a-tag
                          v-if="customerSummary.initailDoctor"
                          color="cyan"
                          size="small"
                          style="margin: 0; font-size: 10px; padding: 0 4px; height: 18px; line-height: 16px"
                        >
                          初检：{{ customerSummary.initailDoctor }}
                        </a-tag>
                        <a-tag
                          v-if="customerSummary.chiefDoctor"
                          color="blue"
                          size="small"
                          style="margin: 0; font-size: 10px; padding: 0 4px; height: 18px; line-height: 16px"
                        >
                          主检：{{ customerSummary.chiefDoctor }}
                        </a-tag>
                        <a-tag
                          v-if="customerSummary.auditor"
                          color="green"
                          size="small"
                          style="margin: 0; font-size: 10px; padding: 0 4px; height: 18px; line-height: 16px"
                        >
                          审核：{{ customerSummary.auditor }}
                        </a-tag>
                      </div>

                      <!-- 详细信息 -->
                      <div
                        style="display: flex; align-items: center; gap: 8px; font-size: 11px; color: #8c8c8c; flex: 1; min-width: 0; overflow: hidden"
                      >
                        <span v-if="currentReg.examCategory" style="flex-shrink: 0">{{ currentReg.examCategory }}</span>
                        <span v-if="currentReg.regTime" style="flex-shrink: 0">{{ currentReg.regTime }}</span>
                        <span v-if="currentReg.riskFactor_dictText" style="flex-shrink: 0">{{ currentReg.riskFactor_dictText }}</span>
                        <span
                          v-if="currentReg.companyName"
                          style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                        >
                          {{ currentReg.companyName }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧头像 -->
                  <div style="30%">
                    <div
                      class="avatar-container-minimal"
                      style="
                        width: 46px;
                        height: 46px;
                        border: 1px solid #f0f0f0;
                        border-radius: 3px;
                        overflow: hidden;
                        cursor: pointer;
                        background: #fafafa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s ease;
                        position: relative;
                      "
                      @click="previewAvatar"
                    >
                      <img
                        v-if="currentReg.avatar"
                        :src="getFileAccessHttpUrl(currentReg.avatar)"
                        style="width: 100%; height: 100%; object-fit: cover"
                        alt="用户头像"
                      />
                      <UserOutlined v-else style="font-size: 10px; color: #d9d9d9" />
                    </div>
                  </div>
                </div>
              </a-card>
            </div>

            <!-- 无用户数据时显示空状态 -->
            <div v-else>
              <a-card
                size="small"
                style="
                  padding: 6px 10px;
                  background-color: #fff;
                  border: 1px solid #e8e8e8;
                  height: 56px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                <div style="display: flex; align-items: center; justify-content: center; color: #8c8c8c; font-size: 13px">
                  <UserOutlined style="margin-right: 6px; font-size: 16px" />
                  <span>请选择体检人员</span>
                </div>
              </a-card>
            </div>
          </div>
        </div>

        <splitpanes class="default-theme">
          <pane size="25" style="padding: 0">
            <a-card size="small">
              <template #extra>
                <a-space>
                  <a-button @click="previewAllPic">全部图片</a-button>
                  <a-button @click="openDataModal">历史数据</a-button>
                  <a-spin :spinning="btnLoading">
                    <a-dropdown>
                      <template #overlay>
                        <a-menu @click="handleMenuClick">
                          <a-menu-item key="fixLisData"> 更新检验数据</a-menu-item>
                          <a-menu-item key="fixCheckData"> 更新检查数据</a-menu-item>
                          <a-menu-item key="navLis"> 检验系统</a-menu-item>
                          <a-menu-item key="navCheck"> 检查系统</a-menu-item>
                          <a-menu-item key="occuHistory"> 职业病问卷</a-menu-item>
                          <a-menu-item key="printApply"> 打印导引单</a-menu-item>
                          <a-menu-item key="printerSetup"> 打印机设置</a-menu-item>
                          <a-menu-item key="healthQuest"> 健康问卷</a-menu-item>
                        </a-menu>
                      </template>

                      <a-button size="small">
                        更多
                        <DownOutlined />
                      </a-button>
                    </a-dropdown>
                  </a-spin>
                </a-space>
              </template>
              <div style="padding: 0; height: 90vh; overflow-y: auto">
                <customer-reg-item-group-status ref="itemGroupStatus" @load-data="handleItemGroupStatus" />
              </div>
            </a-card>
          </pane>
          <pane size="75" style="padding: 0">
            <a-card size="small">
              <template #extra>
                <a-space>
                  <!-- 隐藏保存初检按钮，直接进行总检 -->
                  <!-- <a-button
                    type="primary"
                    size="middle"
                    @click="saveOrUpdateSummary('pre')"
                    :disabled="customerSummary.status == '审核通过'"
                    v-if="hasPermission('summary:presave')"
                  >
                    <SaveOutlined />
                    保存初检
                  </a-button> -->
                  <a-button
                    type="primary"
                    size="middle"
                    :disabled="customerSummary.status == '审核通过'"
                    @click="saveOrUpdateSummary('normal')"
                    v-if="hasPermission('summary:customer_reg_summary:add')"
                  >
                    <SaveOutlined />
                    保存总检
                  </a-button>
                  <a-button type="primary" size="middle" @click="openAuditModal" v-if="hasPermission('summary:summary_audit_record:add')">
                    <AuditOutlined />
                    审核
                  </a-button>
                  <a-button
                    type="primary"
                    size="middle"
                    @click="revokeAudit"
                    v-if="
                      (currentReg.summaryStatus == '审核通过' || currentReg.summaryStatus == '驳回') &&
                      hasPermission('summary:summary_audit_record:add')
                    "
                  >
                    <ClearOutlined />
                    撤销
                  </a-button>
                  <a-badge :count="recheckTotal" color="cyan">
                    <a-button type="primary" size="middle" @click="openRecheckNotifyPannel">
                      <CarryOutOutlined />
                      复查
                    </a-button>
                  </a-badge>
                  <a-button type="dashed" size="middle" @click="openAuditRecordModal">
                    <HistoryOutlined />
                  </a-button>
                  <a-spin :spinning="reportDataLoading">
                    <a-radio-group>
                      <a-radio-button value="large" @click="previewReport">预览</a-radio-button>
                      <a-radio-button value="default" @click="printReport">
                        <PrinterOutlined />
                        打印报告
                      </a-radio-button>
                    </a-radio-group>
                    <!--                    <a-button type="primary" size="middle" @click="previewReport"><EyeOutlined />预览报告</a-button>
                  <a-button type="primary" size="middle" @click="saveOrUpdateSummary"><PrinterOutlined />打印报告</a-button>-->
                    <a-select
                      placeholder="请选择报告模版"
                      v-model:value="currentReportTemplateId"
                      size="middle"
                      style="width: 150px"
                      :options="reportTemplateList"
                      @change="handleReportTemplateChange"
                    />
                  </a-spin>
                </a-space>
              </template>
              <a-row :gutter="2">
                <!--                <a-col :span="10">
                                  <a-card size="small" title="异常汇总" :bordered="false" style="margin-bottom: 2px; margin-top: 2px">
                                    <template #extra>
                                      <a-space v-if="customerSummary.status != '审核通过'">
                                        &lt;!&ndash;<tiptap v-model="departSummary" />&ndash;&gt;
                                        <a-popconfirm title="确定更新序号吗?" @confirm="rearrangeNumbers">
                                          <FieldBinaryOutlined />
                                        </a-popconfirm>
                                        <a-divider type="vertical" />
                                        <a-popconfirm title="将覆盖现有内容，确定刷新吗?" @confirm="getAbnormalSummary">
                                          <SyncOutlined />
                                        </a-popconfirm>

                                        <a-divider type="vertical" />
                                        <FullscreenOutlined @click="openEditor('编辑汇总')" />
                                      </a-space>
                                    </template>

                                    <div style="height: 80vh; padding-bottom: 8px; overflow-y: auto">
                                      <a-spin :spinning="departSummaryLoading">
                                        <a-textarea
                                          :rows="25"
                                          :autosize="false"
                                          :showCount="true"
                                          style="font-size: 15px; line-height: 1.8"
                                          placeholder="请输入汇总"
                                          v-model:value="departSummary"
                                          @mouseup="handleMouseUp"
                                          v-if="
                                            customerSummary.status != '审核通过' &&
                                            (currentReg.reportEditLockFlag != '1' || user.username == currentReg.reportEditLockBy)
                                          "
                                        />
                                        <a-textarea
                                          :rows="20"
                                          style="font-size: 15px; line-height: 1.8"
                                          :readonly="true"
                                          placeholder="请输入汇总"
                                          v-model:value="departSummary"
                                          v-else
                                        />
                                      </a-spin>
                                    </div>
                                  </a-card>
                                </a-col>-->
                <a-col :span="24">
                  <ZyConclusionDetailList ref="zyConclusionDetailList" />
                </a-col>
              </a-row>
            </a-card>

            <a-float-button
              shape="square"
              description="提醒"
              type="primary"
              :style="{
                right: '24px',
              }"
              :badge="{ count: departmentTipCount, showZero: false }"
              @click="openDepartTipDrawer"
            >
              <template #icon>
                <AlertOutlined />
              </template>
            </a-float-button>
          </pane>
        </splitpanes>
      </a-col>
    </a-row>

    <!--    <Report ref="reportRef" />-->
    <upload-manage-modal :preview-file-list="fileList" title="图片管理" ref="registerUploadModal" @ok="handlePicChange" />
    <customer-reg-depart-tip-drawer
      ref="departTipRef"
      :regId="currentReg?.id"
      :departmentId="currentDepartments"
      @loaded="handleDepartTipDrawerLoaded"
    />

    <ComprehensiveInquiryQuestionnaire :customer-reg="currentReg" ref="inquiryModal" />
    <jimu-report-modal ref="jimuReportModal" title="历史结果" width="80%" :report-id="historyResultReportId" />
    <summary-audit-record-modal ref="summaryAuditRecordModal" @success="handleAuditOk" />
    <summary-audit-record-list-modal ref="summaryAuditRecordListModal" />
    <recheck-notify-pannel-modal ref="recheckNotifyPannelModal" @cancel="countRecheck" />
    <editor-modal ref="editorModal" @ok="setDepartSummaryText" />
    <PrinterSetupModal ref="printerSetupModalRef" />
    <Report ref="reportRef" />
    <HealthQuestAddModal ref="healthQuestAddModal" @ok="handleQuestOk" />

    <!-- 记录选择模态框 -->
    <CustomerRegSelectModal ref="customerRegSelectModal" @select="handleRecordSelect" @cancel="handleModalCancel" />

    <!-- 记录详情模态框 -->
    <CustomerRegDetailModal ref="customerRegDetailModal" @select="handleRecordSelect" @cancel="handleModalCancel" />
  </div>
</template>

<script lang="ts" setup name="SummaryPannel">
  import { computed, defineAsyncComponent, nextTick, onMounted, provide, reactive, ref, unref, watch } from 'vue';
  import { AdviceBean, CustomerRegCriticalItem, CustomerRegSummary, ICustomerReg, ICustomerRegItemResult } from '#/types';
  import { fetchCheckData, fetchLisData, getCriticalItem, saveItemResult } from '@/views/station/Station.api';
  import {
    getGeneratedSummaryAdviceByReg,
    getSummaryAdviceByReg,
    getSummaryAdviceByText,
    listAbnormalSummaryByReg,
    querySummaryByRegId,
    revokeSummaryStatus,
    saveHealthCardResult,
    saveSummary,
    updateReportPrintTimes,
  } from '@/views/summary/Summary.api';
  import { list as listAditRecord } from './SummaryAuditRecord.api';
  import { Form, message, SelectProps, theme } from 'ant-design-vue';
  import UploadManageModal from '@/components/Upload/src/UploadManageModal.vue';
  import { preview } from 'vue3-image-preview';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import CustomerRegDepartTipDrawer from '@/views/station/CustomerRegDepartTipDrawer.vue';
  import draggable from 'vuedraggable';
  import {
    AlertOutlined,
    AuditOutlined,
    CarryOutOutlined,
    ClearOutlined,
    CloseCircleOutlined,
    DownOutlined,
    FieldBinaryOutlined,
    FullscreenOutlined,
    HistoryOutlined,
    LockOutlined,
    MenuOutlined,
    PlusOutlined,
    PrinterOutlined,
    ReloadOutlined,
    SaveOutlined,
    SearchOutlined,
    SortAscendingOutlined,
    SyncOutlined,
    UnlockOutlined,
    UnorderedListOutlined,
    UpOutlined,
    UserOutlined,
  } from '@ant-design/icons-vue';
  import JimuReportModal from '@/components/Report/JimuReportModal.vue';
  import CustomerRegList4Summary from '@/views/summary/CustomerRegList4Summary.vue';
  import SummaryAdviceListOfPannel from '@/views/summary/SummaryAdviceListOfPannel.vue';
  import _ from 'lodash';
  import SummaryAuditRecordModal from '@/views/summary/components/SummaryAuditRecordModal.vue';
  import SummaryAuditRecordListModal from '@/views/summary/components/SummaryAuditRecordListModal.vue';
  import RecheckNotifyPannelModal from '@/views/recheck/RecheckNotifyPannelModal.vue';
  import { countByCustomerRegId } from '@/views/recheck/RecheckNotify.api';
  import ZyConclusionDetailList from '@/views/summary/ZyConclusionDetailList.vue';
  import { getReportData, updateReportEditLockFlag } from '@/views/summary/CustomerRegSummary.api';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { getDefaultIdOfType, getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { PrinterType, printReportDirect } from '@/utils/print';
  import EditorModal from '@/views/summary/EditorModal.vue';
  import PrinterSetupModal from '@/views/reg/PrinterSetupModal.vue';
  import { getGuidanceSheet, updateGuidancePrintTimes, updateHealthQuestId, updateReportId } from '@/views/reg/CustomerReg.api';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { buildUUID } from '@/utils/uuid';
  import { addAdvice } from '@/views/summary/SummaryAdvice.api';
  import { useQrScanner } from '@/hooks/scanner/useQrScanner';
  import { listReg } from '@/views/summary/CustomerRegSummary.api';
  import CustomerRegItemGroupStatus from '@/views/summary/components/CustomerRegItemGroupStatus.vue';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import Report from '@/components/Report/Report.vue';
  import HealthQuestAddModal from '@/views/quest/components/HealthQuestAddModal.vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JDictSelectTag from '../../components/Form/src/jeecg/components/JDictSelectTag.vue';
  import CustomerRegList4Summary4Occu from '@/views/summary/CustomerRegList4Summary4Occu.vue';
  import CustomerRegSelectModal from '@/views/summary/components/CustomerRegSelectModal.vue';
  import CustomerRegDetailModal from '@/views/summary/components/CustomerRegDetailModal.vue';

  const ComprehensiveInquiryQuestionnaire = defineAsyncComponent(() => import('@/views/occu/components/ComprehensiveInquiryQuestionnaire.vue'));

  const userStore = useUserStore();
  const user = userStore.getUserInfo;

  //ai总检
  const open = ref<boolean>(false);

  const currentPannel = ref('regList');

  const { hasPermission } = usePermission();
  const { token } = theme.useToken();

  const { createConfirm, createErrorModal } = useMessage();
  const { useToken } = theme;

  const editorModal = ref();
  const summaryPannelKey = ref('health');
  const healthQuestAddModal = ref(null);

  // 新增的模态框引用
  const customerRegSelectModal = ref(null);
  const customerRegDetailModal = ref(null);

  /**职业检*/
  const zyConclusionDetailList = ref();

  async function handleSummaryPannelTab(key) {
    summaryPannelKey.value = key;
  }

  // 抽屉控制方法
  function openDrawer() {
    visible.value = true;
    // 抽屉打开时同步参数
    nextTick(() => {
      syncParamsToDrawer();
    });
  }

  function closeDrawer() {
    visible.value = false;
  }

  function afterClose() {
    // 抽屉关闭后的清理工作
    searchInput.value = '';

    // 从抽屉组件同步参数回主组件
    syncParamsFromDrawer();

    // 清理抽屉组件的搜索状态，但保留用户可能设置的条件
    // 这样下次打开抽屉时，用户的搜索条件还在
  }

  // 扫码枪处理
  const onScan = (input: string) => {
    // 处理扫码枪输入的体检号
    if (input && input.length >= 6) {
      // 体检号通常6位以上
      searchInput.value = input;
      handleSearch();
    }
  };

  // 使用扫码枪监听
  useQrScanner({
    threshold: 500,
    onScan,
  });

  // 检查是否有正在编辑的内容
  function checkEditingStatus(): Promise<boolean> {
    return new Promise((resolve) => {
      // 检查是否有未保存的内容
      const hasUnsavedContent =
        contentChanged.value ||
        (departSummary.value && departSummary.value.trim() !== '') ||
        (adviceList.value && adviceList.value.some((item) => item.name || item.content));

      if (hasUnsavedContent && currentReg.value?.id) {
        createConfirm({
          iconType: 'warning',
          title: '确认切换提示',
          content: '当前有未保存的总检记录，切换将丢失当前编辑内容，是否继续？',
          onOk: () => {
            resolve(true);
          },
          onCancel: () => {
            resolve(false);
          },
        });
      } else {
        resolve(true);
      }
    });
  }

  // 快速搜索功能 - 直接搜索并加载数据，不打开抽屉
  async function handleSearch() {
    if (!searchInput.value || !searchInput.value.trim()) {
      message.warn('请输入体检号');
      return;
    }

    // 检查编辑状态
    const canProceed = await checkEditingStatus();
    if (!canProceed) {
      return;
    }

    searchLoading.value = true;
    try {
      // 直接通过API搜索体检记录
      const examNo = searchInput.value.trim();

      // 调用API搜索
      const searchResult = await searchByExamNo(examNo);

      if (searchResult && searchResult.length > 0) {
        // 找到记录，直接加载第一条
        const selectedRecord = searchResult[0];
        handleRegTableRowClick(selectedRecord);
        message.success(`找到体检记录：${selectedRecord.name}`);

        // 同步更新表单搜索条件
        queryParam.examNo = examNo;
      } else {
        message.warning('未找到匹配的体检记录');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败：' + (error.message || '未知错误'));
    } finally {
      searchLoading.value = false;
    }
  }

  // 根据体检号搜索记录的API调用
  async function searchByExamNo(examNo: string) {
    try {
      // 调用后端API搜索
      const searchParams = {
        examNo: examNo,
        current: 1,
        size: 10, // 限制返回数量
      };

      const response = await listReg(searchParams);

      if (response && response.records) {
        return response.records;
      }
      return [];
    } catch (error) {
      console.error('搜索API调用失败:', error);
      throw error;
    }
  }

  // 清空快速搜索
  function handleClearSearch() {
    searchInput.value = '';
    // 同步清空表单中的体检号
    queryParam.examNo = '';

    // 如果抽屉是打开的，也清空列表搜索条件
    if (customerRegList.value) {
      if (customerRegList.value.setQueryParams) {
        customerRegList.value.setQueryParams({ examNo: '' });
      } else if (customerRegList.value.queryParam) {
        customerRegList.value.queryParam.examNo = '';
      }
    }
  }

  // 预览头像
  function previewAvatar() {
    if (currentReg.value?.avatar) {
      preview({
        images: [getFileAccessHttpUrl(currentReg.value.avatar)],
      });
    } else {
      message.info('暂无头像');
    }
  }

  /**
   * 同步参数到抽屉组件
   */
  function syncParamsToDrawer() {
    if (customerRegList.value) {
      const listComponent = customerRegList.value;
      if (listComponent.setQueryParams) {
        // 只传递有值的参数
        const paramsToSync = {};
        if (queryParam.name) paramsToSync.name = queryParam.name;
        if (queryParam.examNo) paramsToSync.examNo = queryParam.examNo;
        if (queryParam.idCard) paramsToSync.idCard = queryParam.idCard;

        listComponent.setQueryParams(paramsToSync);
      }
    }
  }

  /**
   * 从抽屉组件同步参数回来
   */
  function syncParamsFromDrawer() {
    if (customerRegList.value && customerRegList.value.getQueryParams) {
      const drawerParams = customerRegList.value.getQueryParams();
      // 只同步用户输入的参数，不同步固定参数
      if (drawerParams.name !== undefined) queryParam.name = drawerParams.name || '';
      if (drawerParams.examNo !== undefined) queryParam.examNo = drawerParams.examNo || '';
      if (drawerParams.idCard !== undefined) queryParam.idCard = drawerParams.idCard || '';
    }
  }

  // 表单搜索方法 - 使用静默搜索
  async function searchQuery() {
    // 检查编辑状态
    const canProceed = await checkEditingStatus();
    if (!canProceed) {
      return;
    }

    // 如果有体检号，使用静默搜索
    if (queryParam.examNo && queryParam.examNo.trim()) {
      searchLoading.value = true;
      try {
        const examNo = queryParam.examNo.trim();
        const searchResult = await searchByExamNo(examNo);

        if (searchResult && searchResult.length == 1) {
          // 找到记录，直接加载第一条
          const selectedRecord = searchResult[0];
          if (selectedRecord.examCategory != '职业病体检') {
            // 弹出详细弹窗，展示查询到的登记记录详情，提示不是职业病体检，无法在当前页面处理
            customerRegDetailModal.value?.open(selectedRecord);
          } else {
            handleRegTableRowClick(selectedRecord);
            message.success(`找到体检记录：${selectedRecord.name}`);
          }
        } else if (searchResult && searchResult.length > 1) {
          // 弹出弹窗，展示列表，让用户选择
          customerRegSelectModal.value?.open(searchResult);
        } else {
          message.warning('未找到匹配的体检记录');
        }
      } catch (error) {
        console.error('搜索失败:', error);
        message.error('搜索失败：' + (error.message || '未知错误'));
      } finally {
        searchLoading.value = false;
      }
      return;
    }

    // 如果没有体检号但有其他搜索条件，打开抽屉执行搜索
    if (queryParam.name || queryParam.idCard) {
      visible.value = true;

      // 等待抽屉打开后再执行搜索
      await nextTick();

      // 同步参数到抽屉组件
      syncParamsToDrawer();

      if (customerRegList.value) {
        // 执行搜索
        await customerRegList.value.searchQuery();
      }
    } else {
      message.warn('请输入搜索条件');
    }
  }

  // 重置搜索表单
  function searchReset() {
    // 重置表单
    queryParam.name = '';
    queryParam.examNo = '';
    queryParam.idCard = '';

    // 清空快速搜索
    searchInput.value = '';

    // 重置表单验证状态
    if (formRef.value) {
      formRef.value.resetFields();
    }

    // 如果抽屉是打开的，也重置列表搜索条件
    if (visible.value && customerRegList.value) {
      customerRegList.value.clearQueryParams();
    }
  }

  /**
   * 清空所有搜索相关状态
   */
  function clearAllSearchStates() {
    // 清空主组件搜索参数
    queryParam.name = '';
    queryParam.examNo = '';
    queryParam.idCard = '';
    searchInput.value = '';

    // 清空抽屉组件搜索参数
    if (customerRegList.value) {
      customerRegList.value.clearQueryParams();
    }

    // 重置表单验证状态
    if (formRef.value) {
      formRef.value.resetFields();
    }
  }

  const btnLoading = ref(false);

  // 抽屉控制
  const visible = ref(false);

  // 搜索相关
  const searchInput = ref('');
  const searchLoading = ref(false);

  // 表单搜索相关
  const formRef = ref();
  const queryParam = reactive({
    name: '',
    examNo: '',
    idCard: '',
  });

  // 表单布局
  const labelCol = { span: 6 };
  const wrapperCol = { span: 18 };

  /**体检人员列表部分*/
  const currentDepartments = ref('');
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>({});

  const customerReg2Provide = ref();
  provide('customerReg4Summary', customerReg2Provide);
  /**科室汇总部分*/
  const originDepartGroupList = ref([]);
  const departSummaryLoading = ref(false);
  const departSummary = ref('');

  function setDepartSummaryText(text) {
    departSummary.value = text;
  }

  function handleAuditOk(data) {
    if (data) {
      customerRegList.value?.reloadCurrent();
    }
  }

  // 增加方法：自动整理序号
  const rearrangeNumbers = () => {
    // 按换行符分割文本
    const lines = departSummary.value.split('\n').filter((line) => line.trim() !== '');

    // 生成带有序号的新内容
    const newContent = lines
      .map((line, index) => {
        // 使用正则替换旧序号（支持"数字."或"数字、"等形式）
        return `${index + 1}、${line.replace(/^\d+[\.、]\s*/, '')}`;
      })
      .join('\n');

    // 更新汇总内容（只有变化时才更新避免死循环）
    if (newContent !== departSummary.value) {
      departSummary.value = newContent;
    }
  };

  // 增加防抖处理（500ms延迟）
  const debouncedRearrange = _.debounce(rearrangeNumbers, 500);

  function getAbnormalSummary() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    if (summaryAdviceLoading.value) {
      message.warn('当前正在刷新，请稍等');
      return;
    }
    departSummaryLoading.value = true;
    listAbnormalSummaryByReg({ customerRegId: currentReg.value.id })
      .then((res) => {
        departSummary.value = res;
      })
      .finally(() => {
        departSummaryLoading.value = false;
      });
  }

  /***健康证*/
  const healthCardForm = reactive<Record<string, any>>({
    healthCardResult: '合格',
  });
  const useForm = Form.useForm;

  async function saveHealthCardSummary() {
    if (customerSummary.value?.healthCardResult == null) {
      message.error('请选择健康证结果');
      return;
    }
    let data = {
      customerRegId: currentReg.value.id,
      summaryId: customerSummary.value.id,
      healthCardResult: customerSummary.value.healthCardResult,
    };
    await saveHealthCardResult(data);
  }

  /**项目组合部分*/
  const itemGroupStatus = ref();
  const departGroupList = ref([]);

  function handleItemGroupStatus(data) {
    departGroupList.value = data;
    originDepartGroupList.value = data;
  }

  /**复查项目部分*/
  const recheckTotal = ref(0);
  const recheckNotifyPannelModal = ref();

  function countRecheck() {
    if (!currentReg.value?.id) {
      return;
    }
    countByCustomerRegId({ customerRegId: currentReg.value.id }).then((res) => {
      recheckTotal.value = res;
    });
  }

  function openRecheckNotifyPannel() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    if (!customerSummary.value?.id) {
      message.error('请先保存总检结果');
      return;
    }
    recheckNotifyPannelModal.value?.open(currentReg.value.id, customerSummary.value.id);
  }

  async function handleQuestOk(personalQuestId) {
    await updateHealthQuestId({ id: currentReg.value.id, personalQuestId: personalQuestId });
    customerRegList.value?.reloadCurrent();
  }

  // 处理记录选择事件
  function handleRecordSelect(record: ICustomerReg) {
    handleRegTableRowClick(record);
    message.success(`已选择体检记录：${record.name}`);
  }

  // 处理模态框取消事件
  function handleModalCancel() {
    // 模态框取消时的处理逻辑，目前无需特殊处理
  }

  /**总检建议*/
  const aiSummary = ref(true);
  const historyResultReportId = ref();
  const abnormalDepartSummary = ref(true);
  const customerSummary = ref<CustomerRegSummary>({});
  provide('customerSummary', customerSummary);
  const adviceKeywords = ref('');
  const adviceList = ref<AdviceBean[]>([]);
  const adviceChkStatus = computed(() => {
    return adviceList.value.length > 0 && adviceList.value.every((item) => item.chk);
  });
  const summaryAdviceLoading = ref(false);
  const adviceSpinText = ref('正在获取建议');

  // 添加自动保存相关变量
  const autoSaveEnabled = ref(true);
  const isSaving = ref(false);
  const lastSaveTime = ref('');
  const contentChanged = ref(false);
  const initialLoad = ref(true);

  // 拖拽相关状态
  const isDragging = ref(false);
  const dragStartIndex = ref(-1);
  const dragTargetIndex = ref(-1);

  function removeAdviceBean(adviceBean) {
    //找到key相同元素的index，然后删除
    let index = adviceList.value.findIndex((item) => item.key == adviceBean.key);
    if (index != -1) {
      adviceList.value.splice(index, 1);
    }
  }

  function addSummaryAdvice(adviceBean) {
    if (adviceBean.name == '' || adviceBean.content == '') {
      message.error('请填写建议名称和内容');
      return;
    }
    let data = {
      keywords: adviceBean.name,
      adviceContent: adviceBean.content,
      source: '手动添加',
    };
    addAdvice(data);
  }

  // 拖拽处理函数
  const handleDragStart = (e) => {
    isDragging.value = true;
    dragStartIndex.value = e.oldIndex;
    console.log('SummaryPannel: Drag started at index', e.oldIndex);
  };

  const handleDragEnd = (e) => {
    isDragging.value = false;
    dragStartIndex.value = -1;
    dragTargetIndex.value = -1;

    // 如果位置发生变化，更新seq属性
    if (e.oldIndex !== e.newIndex) {
      console.log(`SummaryPannel: Item moved from ${e.oldIndex} to ${e.newIndex}`);

      // 更新所有项目的seq属性
      adviceList.value.forEach((item, index) => {
        item.seq = index + 1;
      });

      console.log('SummaryPannel: Drag ended, seq updated');
    }
  };

  const handleDragChange = (e) => {
    if (e.moved) {
      dragTargetIndex.value = e.moved.newIndex;
      console.log('SummaryPannel: Drag target changed to index', e.moved.newIndex);
    }
  };

  // 保留原有的上移下移功能作为备选方案
  function moveUp(index) {
    if (index > 0) {
      const temp = adviceList.value[index];
      adviceList.value.splice(index, 1);
      adviceList.value.splice(index - 1, 0, temp);

      // 更新seq属性
      adviceList.value.forEach((item, idx) => {
        item.seq = idx + 1;
      });
    }
  }

  function moveDown(index) {
    if (index < adviceList.value.length - 1) {
      const temp = adviceList.value[index];
      adviceList.value.splice(index, 1);
      adviceList.value.splice(index + 1, 0, temp);

      // 更新seq属性
      adviceList.value.forEach((item, idx) => {
        item.seq = idx + 1;
      });
    }
  }

  function getSettingFromLocalStorage(key: string, defaultValue: boolean): boolean {
    const value = localStorage.getItem(key);
    return value !== null ? JSON.parse(value) : defaultValue;
  }

  function setSettingToLocalStorage(key: string, value: boolean) {
    localStorage.setItem(key, JSON.stringify(value));
  }

  function handleAiChange(e) {
    aiSummary.value = e;
    setSettingToLocalStorage('aiSummary', aiSummary.value);
    //getAdviceByText();
  }

  function handleAutoSaveChange(enabled) {
    autoSaveEnabled.value = enabled;
    setSettingToLocalStorage('autoSaveEnabled', enabled);
    if (enabled && contentChanged.value) {
      // 如果开启自动保存并且内容已变更，则执行一次保存
      debouncedAutoSave();
    }
  }

  function unlockReportEdit() {
    updateReportEditLockFlag({
      customerRegId: currentReg.value.id,
      reportEditLockFlag: '0',
    }).then(() => {
      message.success('解锁成功');
      customerRegList.value?.reloadCurrent();
    });
  }

  function handleDepartSummaryChange(e) {
    abnormalDepartSummary.value = e;
    setSettingToLocalStorage('abnormalDepartSummary', abnormalDepartSummary.value);
    getSummaryAdvice();
  }

  function getAdviceByText(aiSummaryFlag) {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    adviceSpinText.value = `正在获取${aiSummaryFlag ? 'AI' : '知识库'}建议`;
    summaryAdviceLoading.value = true;
    initialLoad.value = true;

    let data = {
      summaryText: departSummary.value,
      customerRegId: currentReg.value.id,
      aiSummary: aiSummaryFlag,
      abnormalDepartSummary: abnormalDepartSummary.value,
    };
    getSummaryAdviceByText(data)
      .then((res) => {
        let adviceListTemp = res.adviceList || [];
        let regId = res.customerRegId;
        if (currentReg.value.id != regId) {
          message.error('体检人员已变更，本次建议无效');
          return;
        }
        adviceList.value = adviceListTemp.map((item) => {
          return {
            seq: item.seq,
            name: item.name,
            content: item.content,
            chk: false,
            source: item.source,
            key: buildUUID(),
          };
        });
        // 重置自动保存状态，因为这是一个手动刷新操作
        resetAutoSaveStatus();
        // 设置延迟，确保数据加载和渲染完成后才允许自动保存
        setTimeout(() => {
          initialLoad.value = false;
        }, 1000);
      })
      .finally(() => {
        summaryAdviceLoading.value = false;
      });
  }

  function confirmReplaceAdvice() {
    return new Promise<void>((resolve, reject) => {
      if (adviceList.value.length == 0) {
        resolve();
      } else {
        createConfirm({
          iconType: 'warning',
          title: '获取自动建议提示',
          content: '当前建议列表不为空，是否覆盖？',
          onOk: () => {
            resolve();
          },
          onCancel: () => {
            reject();
          },
        });
      }
    });
  }

  function getGeneratedSummaryAdvice() {
    confirmReplaceAdvice()
      .then(() => {
        summaryAdviceLoading.value = true;
        getGeneratedSummaryAdviceByReg({ customerRegId: currentReg.value?.id })
          .then((res) => {
            if (res.length == 0) {
              message.info('没有自动生成的建议');
            } else {
              adviceList.value = res.map((item) => {
                return {
                  seq: item.seq,
                  name: item.name,
                  content: item.content,
                  chk: false,
                  source: '自动建议',
                };
              });
            }
          })
          .finally(() => {
            summaryAdviceLoading.value = false;
          });
      })
      .catch(() => {
        //summaryAdviceLoading.value = false;
      });
  }

  function getSummaryAdvice() {
    departSummary.value = '';
    adviceList.value = [];
    adviceSpinText.value = `正在获取${aiSummary.value ? 'AI' : '知识库'}建议`;
    summaryAdviceLoading.value = true;
    initialLoad.value = true;

    getSummaryAdviceByReg({
      customerRegId: currentReg.value.id,
      aiSummary: aiSummary.value,
      abnormalDepartSummary: abnormalDepartSummary.value,
    })
      .then((res) => {
        customerSummary.value = res;
        recheckTotal.value = res.recheckCount;
        departSummary.value = res.characterSummary;
        if (res.id) {
          getAditRecord(customerSummary.value.id);
        }
        if (res.summaryJson.length > 0) {
          adviceList.value = res.summaryJson.map((item) => {
            return {
              seq: item.seq,
              name: item.name,
              content: item.content,
              chk: false,
              source: item.source,
              key: buildUUID(),
            };
          });
        } else {
          initAdviceList();
        }

        // 重置自动保存状态，因为已经从服务器获取了最新数据
        resetAutoSaveStatus();

        // 设置延迟，确保数据加载和渲染完成后才允许自动保存
        setTimeout(() => {
          initialLoad.value = false;
        }, 1000);
      })
      .finally(() => {
        summaryAdviceLoading.value = false;
      });
  }

  function handleAdviceChk(e) {
    adviceList.value.forEach((item) => {
      item.chk = e.target.checked;
    });
  }

  function openEditor(title) {
    editorModal.value?.open(departSummary.value, title);
  }

  function initAdviceList() {
    adviceList.value = [];
    adviceList.value.push({
      seq: adviceList.value.length + 1,
      name: '',
      content: '',
      chk: false,
      source: '手动添加',
      key: buildUUID(),
    });
  }

  function addEmptyAdvice() {
    adviceList.value.push({
      seq: adviceList.value.length + 1,
      name: '',
      content: '',
      chk: false,
      source: '手动添加',
      key: buildUUID(),
    });
  }

  function handleRemoveAdvice() {
    //如果没有选中的建议，提示
    if (adviceList.value.length == 0 || !adviceList.value.some((item) => item.chk)) {
      message.warn('请选择要删除的建议');
      return;
    }
    adviceList.value = adviceList.value.filter((item) => !item.chk);
  }

  function adoptAdvice(advice) {
    //找到第一个chk为true的元素的index
    let index = adviceList.value.findIndex((item) => item.chk);
    if (index != -1) {
      let currentAdvice = adviceList.value[index];
      if (currentAdvice) {
        currentAdvice.name = advice.keywords;
        currentAdvice.content = advice.adviceContent;
      }
      return;
    }
    let advide2Add = {
      seq: adviceList.value.length + 1,
      name: advice.keywords,
      content: advice.adviceContent,
      chk: false,
    };
    if (adviceList.value?.length == 0) {
      adviceList.value.push(advide2Add);
    } else {
      //找到最后一个content为空的元素
      let lastElement = adviceList.value.find((item) => !item.content || item.content == '');
      //const lastElement = adviceList.value[adviceList.value.length - 1];
      if (lastElement) {
        lastElement.name = advide2Add.name;
        lastElement.content = advide2Add.content;
      } else {
        adviceList.value.push(advide2Add);
      }
    }
    //如果adviceList最后一个元素name和content都为空，则替换advide
  }

  let handleEditAdviceName = _.debounce((e) => {
    adviceKeywords.value = e.target.value;
  }, 100);

  function handleMouseUp(event) {
    const textArea = event.target;
    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    adviceKeywords.value = textArea.value.substring(start, end);
  }

  function checkAllMajorItemsSummarized(): string[] {
    return originDepartGroupList.value.filter((departGroup) => !departGroup.summaryFlag).map((departGroup) => departGroup.depart.departName);
  }

  async function saveOrUpdateSummary(type: string = '') {
    //校验
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }

    //先调用职业检诊断组件的保存方法
    const saveSuccess = await zyConclusionDetailList.value?.saveAllEditing();
    if (saveSuccess === false) {
      message.error('职业检结论保存失败，请检查后重试');
      return;
    }
    // 移除初检验证，允许直接进行总检
    // if (type == 'normal' && !customerSummary.value.preAuditor) {
    //   message.warn('请先保存初检！');
    //   return;
    // }
    /* if (departSummary.value == '') {
message.warn('请填写汇总');
return;
}*/
    /* if (adviceList.value.length == 0) {
message.warn('请填写建议');
return;
}*/
    const unsummarizedItems = checkAllMajorItemsSummarized();
    if (unsummarizedItems.length > 0) {
      createConfirm({
        iconType: 'warning',
        title: '确认总检提示',
        content: `以下科室未填写小结：${unsummarizedItems.join(', ')}，是否继续保存？`,
        onOk: async () => {
          await performSave(type);
        },
      });
    } else {
      await performSave(type);
    }
  }

  async function performSave(type) {
    let summaryJson = adviceList.value.map((item, index) => ({
      seq: index + 1,
      name: item.name,
      content: item.content,
      source: item.source,
    }));

    let advice = adviceList.value.map((item) => item.content).join('\r\n');
    let postData = {
      id: customerSummary.value.id,
      characterSummary: departSummary.value,
      summaryJson: summaryJson,
      advice: advice,
      type: type,
      customerRegId: currentReg.value.id,
      examNo: currentReg.value.examNo,
    };

    try {
      await saveSummary(postData);
      //fetchData();
      //currentReg.value.summaryStatus = customerSummary.value.status;
      customerRegList.value?.reloadCurrent();

      // 重新获取总检数据以更新 customerSummary，确保 creatorName 等字段正确设置
      if (currentReg.value.id) {
        getSummaryAdvice();
      }

      // 重置自动保存状态
      contentChanged.value = false;
      lastSaveTime.value = formatDateTime();
    } catch (error) {
      console.error(error);
    }
  }

  const summaryAuditRecordModal = ref(null);
  const summaryAuditRecordListModal = ref(null);
  const auditRecordList = ref([]);
  const showAuditTip = ref(false);
  const auditRecord = ref(null);

  function getAditRecord(summaryId) {
    listAditRecord({ summaryId: summaryId }).then((res) => {
      auditRecordList.value = res.records;
      if (auditRecordList.value.length > 0 && auditRecordList.value[0].auditResult == '驳回') {
        showAuditTip.value = true;
        auditRecord.value = auditRecordList.value[0];
      }
    });
  }

  function openAuditModal() {
    if (!customerSummary.value.id) {
      message.warn('请先保存总检信息！');
      return;
    }
    if (!currentReg.value.id) {
      message.warn('请选择体检人员！');
      return;
    }
    // 移除初检验证，允许直接审核
    // if (!customerSummary.value.preAuditor) {
    //   message.warn('尚未初检，不能审核！');
    //   return;
    // }

    // 简化验证逻辑：只要有总检记录ID就可以审核
    // 因为我们已经在上面验证了 customerSummary.value.id 的存在
    // if (!customerSummary.value.creatorName) {
    //   message.warn('尚未总检，不能审核！');
    //   return;
    // }

    summaryAuditRecordModal.value?.add(customerSummary.value, true);
  }

  function openAuditRecordModal() {
    summaryAuditRecordListModal.value?.open(customerSummary.value.id);
  }

  function revokeAudit() {
    if (user.username !== customerSummary.value.auditeBy) {
      message.error('只有审核者本人才能撤销审核');
      return;
    }
    //检查是否已审核
    createConfirm({
      iconType: 'warning',
      title: '撤销审核提示',
      content: '确定要撤销审核吗？',
      onOk: () => {
        let data = {
          summaryId: customerSummary.value.id,
          customerRegId: currentReg.value.id,
        };
        revokeSummaryStatus(data).then((res) => {
          //customerSummary.value.status = res;
          //currentReg.value.summaryStatus = res;
          customerRegList.value?.reloadCurrent();
          //message.success('撤销审核成功！');
        });
      },
    });
  }

  /**科室结果图片*/
  const currentItemResult = ref<ICustomerRegItemResult>({});
  const priewPic = (urls) => {
    preview({
      images: urls.map((item) => getFileAccessHttpUrl(item)),
    });
  };

  const printerSetupModalRef = ref(null);

  function handleMenuClick(menu) {
    //console.log('handleMenuClick', menu);
    if (menu.key === 'allPic') {
      previewAllPic();
    } else if (menu.key === 'occuHistory') {
      openInquiry();
    } else if (menu.key === 'printerSetup') {
      openPrinterSetupModal();
    } else if (menu.key === 'printApply') {
      printCurrentGuide();
    } else if (menu.key === 'hisData') {
      openDataModal();
    } else if (menu.key === 'fixLisData') {
      fixLisData();
    } else if (menu.key === 'fixCheckData') {
      fixCheckData();
    } else if (menu.key === 'navLis') {
      navLis();
    } else if (menu.key === 'navCheck') {
      navPacs();
    } else if (menu.key === 'healthQuest') {
      healthQuest();
    }
  }

  /**打印导引单*/
  async function printCurrentGuide() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录');
      return;
    }
    printGuide(currentReg.value);
    //printGuide(currentReg.value);
  }

  function getGuideTemplate() {
    return new Promise((resolve, reject) => {
      getDefaultIdOfType({ type: '导引单' }).then((res) => {
        if (res.success) {
          let templateId = res.result;
          getTemplateById({ id: templateId }).then((templateRes) => {
            resolve(JSON.parse(templateRes.content));
          });
        }
      });
    });
  }

  async function printGuide(reg) {
    let regUnref = unref(reg);
    if (!regUnref) {
      return;
    }
    try {
      const customerRegDetail = await getGuidanceSheet({ id: regUnref.id });
      if (customerRegDetail.reg.avatar) {
        customerRegDetail.reg.avatar = getFileAccessHttpUrl(customerRegDetail.reg.avatar);
      }
      let template = await getGuideTemplate();
      if (!template) {
        message.error('未找到导引单模板');
        return;
      }
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(customerRegDetail);
      await printReportDirect(template, PrinterType.Guide);
      updateGuidancePrintTimes({ regId: regUnref.id });
    } catch (e) {
      console.log(e);
    }
  }

  function openPrinterSetupModal() {
    printerSetupModalRef.value?.open();
  }

  /**第三方接口url*/
  const lisUrl = ref('');
  const checkUrl = ref('');

  function fetchUrlSetting() {
    querySysParamByCode({ code: 'lis_url' }).then((res) => {
      lisUrl.value = res.result;
    });
    querySysParamByCode({ code: 'check_url' }).then((res) => {
      checkUrl.value = res.result;
    });
  }

  const navLis = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!lisUrl.value) {
      message.error('未配置LIS系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    // Replace {regId} in the URL with the actual regId
    const fullUrl = lisUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const navPacs = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!checkUrl.value) {
      message.error('未配置检查系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    const fullUrl = checkUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const previewAllPic2 = () => {
    const picSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (group.reportPics && group.reportPics.length > 0) {
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });

    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };
  const previewAllPic = () => {
    const picSet = new Set<string>();
    const reportPdfInterfaceSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (!group.reportPdfInterface) {
          // 处理 reportPdfInterface 为 null 的情况
          if (group.reportPics && group.reportPics.length > 0) {
            group.reportPics.forEach((pic) => {
              picSet.add(pic);
            });
            // 注意：这里我们不需要再为 null 创建一个特殊的标记，
            // 因为我们通过检查 reportPdfInterface 的值来隐式地处理了这种情况。
          }
        } else if (!reportPdfInterfaceSet.has(group.reportPdfInterface) && group.reportPics && group.reportPics.length > 0) {
          // 处理非 null 和非空的 reportPdfInterface
          reportPdfInterfaceSet.add(group.reportPdfInterface);
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });
    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };

  function healthQuest() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    healthQuestAddModal.value?.open(currentReg.value);
  }

  const registerUploadModal = ref(null);
  const fileList = ref<string[]>([]);

  function handlePicChange(urls: string[]) {
    //找出新增或删除的图片
    let addList = urls.filter((item) => !fileList.value.includes(item));
    let delList = fileList.value.filter((item) => !urls.includes(item));
    if (addList.length > 0 || delList.length > 0) {
      //更新对应项目的图片
      currentItemResult.value.pic = urls;
      saveItemResult({ resultList: [currentItemResult.value] });
    }
  }

  /**职业病问诊*/
  const inquiryModal = ref(null);

  async function openInquiry() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }

    // 确保DOM更新完成
    await nextTick();
    inquiryModal.value.open(currentReg.value, false);
  }

  const jimuReportModal = ref();

  function openDataModal() {
    jimuReportModal.value?.open({ archivesNum: currentReg.value.archivesNum });
  }

  function fixLisData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchLisData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检验数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检验数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  function fixCheckData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchCheckData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检查数据数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检查数据数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  /**科室提醒抽屉部分*/
  const departmentTipCount = ref(0);
  const departTipRef = ref(null);

  function openDepartTipDrawer() {
    departTipRef.value?.open();
  }

  function handleDepartTipDrawerLoaded(count: number) {
    departmentTipCount.value = count;
  }

  /**危急值提示部分*/
  function showCriticalItems(criticalItems: CustomerRegCriticalItem[], refresh: boolean = false) {
    if (criticalItems.length == 0) {
      return;
    }
    // 根据severityDegree分类（A类和B类）
    const categoryA = criticalItems.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.filter((item) => item.severityDegree === 'B类');

    // 然后据此拼接提示信息
    const messageA = categoryA
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join(' <br/>');
    const messageB = categoryB
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join(' <br/>');

    let title = '';
    if (categoryA.length > 0) {
      title += `发现${categoryA.length}项A类危急值 `;
    }
    if (categoryB.length > 0) {
      title += `${categoryB.length}项B类危急值`;
    }

    let message = '<div style="max-height: 50vh;overflow-y: auto;">';
    if (categoryA.length > 0) {
      message += `<span style="color:#f5222d;font-weight: normal">A类（需要立即进行临床干预，否则
将危及生命或导致严重不良后果的异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageA}</p><br/>`;
    }
    if (categoryB.length > 0) {
      message += `<span style="color:#faad14;font-weight: bold;">B类（需要临床进一步检查以明确诊断和(或)需要
医学治疗疗的重要异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageB}</p>`;
    }
    message += '</div>';

    // 显示提示信息
    createErrorModal({
      title: title,
      content: message,
      iconType: 'warning',
      onOk: () => {
        if (refresh) {
          getCriticalItemByRegId();
        }
      },
    });
  }

  const criticalItems = ref<CustomerRegCriticalItem[]>([]);

  function getCriticalItemByRegId(showTip: boolean = false) {
    getCriticalItem({ regId: currentReg.value.id }).then((res) => {
      if (showTip) {
        showCriticalItems(res, false);
      }
      criticalItems.value = res;
    });
  }

  const criticalTip = computed(() => {
    let tip = '';

    if (criticalItems.value.length == 0) {
      return tip;
    }
    const categoryA = criticalItems.value.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.value.filter((item) => item.severityDegree === 'B类');

    const messageA = categoryA
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join('；');
    const messageB = categoryB
      .map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`)
      .join('；');

    if (categoryA.length > 0) {
      tip += `发现${categoryA.length}项A类危急值：${messageA}；`;
    }
    if (categoryB.length > 0) {
      tip += `发现${categoryB.length}项B类危急值：${messageB}；`;
    }

    return tip;
  });

  function handleRegTableRowClick(selectedRow) {
    // 在切换体检人员前重置自动保存状态
    resetAutoSaveStatus();

    currentReg.value = selectedRow;
    customerReg2Provide.value = selectedRow;

    if (selectedRow.id) {
      // 如果抽屉是打开的，则关闭抽屉
      if (visible.value) {
        visible.value = false;
      }
      currentPannel.value = 'summary';

      // 如果体检分类是"职业病体检"，自动切换到职业病卡片
      if (selectedRow.examCategory === '职业病体检') {
        summaryPannelKey.value = 'occu';
      } else {
        // 其他情况默认显示汇总和建议卡片
        summaryPannelKey.value = 'health';
      }

      fetchData();
      zyConclusionDetailList.value?.refreshDataAndCleanup();
    }
  }

  function resetAutoSaveStatus() {
    contentChanged.value = false;
    isSaving.value = false;
    lastSaveTime.value = '';
    initialLoad.value = true;
  }

  async function fetchData() {
    // 重置审核相关状态
    auditRecordList.value = [];
    showAuditTip.value = false;
    auditRecord.value = null;
    departSummary.value = '';
    adviceList.value = [];
    customerSummary.value = {};

    // 重置自动保存状态
    resetAutoSaveStatus();

    if (currentReg.value.id) {
      getCriticalItemByRegId(true);
      //getSummaryAdvice();

      countRecheck();
      filterTemplateByReg();
      nextTick(() => {
        itemGroupStatus.value?.loadData(currentReg.value.id, 'abnormal');
      });

      querySummaryByRegId({ regId: currentReg.value.id }).then(async (res) => {
        if (res.success) {
          customerSummary.value = res.result;

          departSummary.value = res.result.characterSummary;
          let summaryJson = res.result.summaryJson || [];

          adviceList.value = summaryJson.map((item) => {
            return {
              seq: item.seq,
              name: item.name,
              content: item.content,
              chk: false,
              source: item.source,
              key: buildUUID(),
            };
          });

          if (customerSummary.value.status == '未总检') {
            listAbnormalSummaryByReg({ customerRegId: currentReg.value.id }).then((res2) => {
              departSummary.value = res2;
            });
          }

          /* if (!departSummary.value) {
    listAbnormalSummaryByReg({ customerRegId: currentReg.value.id }).then((res2) => {
      departSummary.value = res2;
      getAdviceByText(aiSummary.value);
    });
  } else {
   if (summaryJson.length > 0) {
      adviceList.value = summaryJson.map((item) => {
        return {
          seq: item.seq,
          name: item.name,
          content: item.content,
          chk: false,
          source: item.source,
          key: buildUUID(),
        };
      });
    } else {
      getAdviceByText(aiSummary.value);
    }
  }*/

          // 重置自动保存状态，因为已经从服务器获取了最新数据
          resetAutoSaveStatus();

          // 设置延迟，确保数据加载和渲染完成后才允许自动保存
          setTimeout(() => {
            initialLoad.value = false;
          }, 1000);
        }
      });
    }
  }

  /**报告预览打印*/
  const reportRef = ref(null);
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const currentReportTemplateId = ref(null);
  const reportDataLoading = ref(false);

  async function previewReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    //获取报告模版内容
    reportDataLoading.value = true;
    try {
      const templateRes = await getTemplateById({ id: currentReportTemplateId.value });
      let template = JSON.parse(templateRes.content);
      const reportDataRes = await getReportData({ customerRegId: currentReg.value.id });
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;
        //需要处理报告中的图片
        //console.log(reportData);
        if (reportData.customerReg.customerAvatar) {
          reportData.customerReg.customerAvatar = getFileAccessHttpUrl(reportData.customerReg.customerAvatar);
        }
        if (reportData.reportImgList?.length > 0) {
          reportData.reportImgList?.forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        }
        //console.log(reportData.groupByFunctionMap?.lab_exam);

        if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap) {
          Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
            reportData.groupByFunctionPicMap[key].forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          });
        }

        //console.log(reportData.groupByFunctionMap.lab_exam);
        if (reportData.summaryAdvice?.auditorSignPic) {
          reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
        }
        if (reportData.summaryAdvice?.preAuditorSignPic) {
          reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
        }
        if (reportData.summaryAdvice?.creatorSignPic) {
          reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
        }
        console.log('=======reportData==========', reportData);
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
        if (!reportRef.value) {
          message.error('报告组件初始化失败！');
          return;
        }
        reportRef.value.open({
          filename: `${currentReg.value.name}的体检报告`,
          template: template,
        });
      } else {
        message.error('获取报告数据失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      reportDataLoading.value = false;
    }
  }

  async function printReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    if (customerSummary.value.status != '审核通过') {
      message.warn('总检未审核，不可以打印报告！');
      return;
    }

    previewReport();

    await updateReportPrintTimes({ id: customerSummary.value.id });

    //获取报告模版内容
    /*reportDataLoading.value = true;
let templateRes = await getTemplateById({ id: currentReportTemplateId.value });
let template = JSON.parse(templateRes.content);
let reportDataRes = await getReportData({ customerRegId: currentReg.value.id });
if (!reportDataRes.success) {
message.error('获取报告数据失败');
reportDataLoading.value = false;
return;
}
let reportData = reportDataRes.result;
if (reportData.reportImgList?.length > 0) {
reportData.reportImgList?.forEach((item) => {
item.text = getFileAccessHttpUrl(item.text);
});
}
if (reportData.summaryAdvice?.auditorSignPic) {
reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
}
if (reportData.summaryAdvice?.preAuditorSignPic) {
reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
}
if (reportData.summaryAdvice?.creatorSignPic) {
reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
}

template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
await printReportDirect(template, PrinterType.Guide);
await updateReportPrintTimes({ regId: currentReg.value.id });
reportDataLoading.value = false;*/
  }

  function fetchReportTemplateList() {
    getReportTemplate({ type: '报告' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
        reportTemplateList.value = res.result.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  }

  function filterTemplateByReg() {
    let reg = currentReg.value;
    if (!reg.id) {
      return;
    }
    if (reg.reportTemplateId) {
      return;
    }
    let examCategory = reg.examCategory;
    let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == examCategory);
    currentReportTemplateId.value = regTemplateList[0]?.id;
    updateReportId({
      regId: reg.id,
      templateId: currentReportTemplateId.value,
    });
  }

  function handleHistoryResultReportId() {
    querySysParamByCode({ code: 'historyResultReportId' }).then((res) => {
      historyResultReportId.value = res.result;
    });
  }

  // 自动保存实现
  const formatDateTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  const autoSave = async () => {
    if (!autoSaveEnabled.value || !contentChanged.value || !currentReg.value?.id || !customerSummary.value?.id) {
      return;
    }

    // 如果正在进行其他操作，不执行自动保存
    if (summaryAdviceLoading.value || departSummaryLoading.value) {
      return;
    }

    // 如果已审核通过或报告被锁定且不是当前用户锁定的，则不执行自动保存
    if (
      customerSummary.value.status === '审核通过' ||
      (currentReg.value.reportEditLockFlag === '1' && user.username !== currentReg.value.reportEditLockBy)
    ) {
      return;
    }

    try {
      isSaving.value = true;

      let summaryJson = adviceList.value.map((item, index) => ({
        seq: index + 1,
        name: item.name,
        content: item.content,
        source: item.source,
      }));

      let advice = adviceList.value.map((item) => item.content).join('\r\n');
      let postData = {
        id: customerSummary.value.id,
        characterSummary: departSummary.value,
        summaryJson: summaryJson,
        advice: advice,
        type: customerSummary.value.preAuditor ? 'normal' : 'pre',
        customerRegId: currentReg.value.id,
        examNo: currentReg.value.examNo,
        autoSaved: true,
      };

      await saveSummary(postData);
      lastSaveTime.value = formatDateTime();
      contentChanged.value = false;
    } catch (error) {
      console.error('自动保存失败:', error);
    } finally {
      isSaving.value = false;
    }
  };

  // 创建防抖处理的自动保存函数 - 3秒内只执行一次
  const debouncedAutoSave = _.debounce(autoSave, 3000);

  // 监听汇总文本变化
  /* watch(departSummary, (newVal, oldVal) => {
if (newVal !== oldVal && !initialLoad.value && autoSaveEnabled.value && customerSummary.value?.id) {
contentChanged.value = true;
debouncedAutoSave();
}
});*/

  // 监听建议列表变化
  watch(
    adviceList,
    (newVal, oldVal) => {
      if (!initialLoad.value && autoSaveEnabled.value && customerSummary.value?.id && adviceList.value.length > 0) {
        contentChanged.value = true;
        debouncedAutoSave();
      }
    },
    { deep: true }
  );

  // 在现有方法中添加以下代码
  const handleReportTemplateChange = (templateId: string) => {
    if (currentReg.value?.id) {
      currentReg.value.reportTemplateId = templateId;
      // 如果需要可以在这里添加保存到后端数据库的逻辑
      updateReportId({
        regId: currentReg.value.id,
        templateId: templateId,
      }).then((res) => {
        if (res.success) {
          message.success('报告模板更新成功');
        } else {
          message.error('报告模板更新失败');
        }
      });
    }
  };

  onMounted(() => {
    aiSummary.value = getSettingFromLocalStorage('aiSummary', true);
    abnormalDepartSummary.value = getSettingFromLocalStorage('abnormalDepartSummary', true);
    autoSaveEnabled.value = getSettingFromLocalStorage('autoSaveEnabled', true);
    handleHistoryResultReportId();
    fetchReportTemplateList();
    fetchUrlSetting();
  });
</script>
<style scoped>
  /* 拖拽相关样式 */
  .advice-item {
    cursor: grab;
    user-select: none;
  }

  .advice-item:hover {
    background-color: #fafafa !important;
    border-color: #f0f0f0 !important;
  }

  .advice-item:active {
    cursor: grabbing;
  }

  /* 拖拽手柄样式 */
  .drag-handle-icon {
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 24px;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    color: #bfbfbf;
    backdrop-filter: blur(4px);
    pointer-events: none;
  }

  .advice-item:hover .drag-handle-icon {
    opacity: 1;
  }

  .drag-handle-icon:hover {
    background-color: #f0f9ff;
    border-color: #91d5ff;
    color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  .drag-handle-active {
    opacity: 1 !important;
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  /* 拖拽状态样式 */
  .ghost-item {
    opacity: 0.5;
    background: #f0f9ff !important;
    border: 2px dashed #1890ff !important;
    border-radius: 6px;
  }

  .chosen-item {
    background-color: #e6f7ff !important;
    border: 1px solid #1890ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15) !important;
  }

  .drag-item {
    background-color: #fff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #1890ff !important;
    border-radius: 6px;
    opacity: 0.9;
  }

  .dragging {
    opacity: 0.6;
    background-color: #f0f9ff !important;
  }

  .drag-target {
    background-color: #f0f9ff !important;
    border-color: #1890ff !important;
  }
</style>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .chosenClass {
    opacity: 1;
    border-style: solid;
    border-width: 1px;
    border-color: v-bind('token.colorPrimary');
  }

  .ghost {
    border: solid 1px v-bind('token.colorPrimary') !important;
  }

  .drag-handle {
    cursor: move; /* Change the cursor to a 'move' icon when hovering over the handle */
  }

  .current-advice {
    border: 1px solid v-bind('token.colorPrimary');
    border-radius: 2px;
  }

  :deep(.ant-card-body) {
    padding: 6px;
  }

  :deep(.ant-collapse-header) {
    padding: 2px 8px;
  }

  .fade-move,
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: scaleY(0.01) translate(30px, 0);
  }

  .fade-leave-active {
    position: absolute;
  }

  .highlight {
    background-color: #f0f0f0;
    transition: background-color 0.3s ease;
  }

  .hint-bar {
    position: fixed;
    top: 95%;
    left: 0;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 0 5px 5px 0;
    z-index: 1000;
  }

  .chosen {
    background-color: #e0e0e0;
  }

  .drag {
    background-color: #c0c0c0;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
  }

  .left-pane {
    background-color: #f0f0f0;
    padding: 20px;
  }

  .right-pane {
    background-color: #e0e0e0;
    padding: 20px;
  }

  /* 极简头像容器样式 */
  .avatar-container-minimal:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .avatar-container-minimal:hover img {
    transform: scale(1.02);
  }

  /* 搜索框样式优化 */
  .ant-input-search .ant-input {
    border-radius: 20px !important;
  }

  .ant-input-search .ant-input-search-button {
    border-radius: 0 20px 20px 0 !important;
  }

  /* 按钮样式优化 */
  .ant-btn-circle {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* a-descriptions样式优化 */
  :deep(.ant-descriptions-item-label) {
    padding-bottom: 0 !important;
  }

  :deep(.ant-descriptions-item-content) {
    padding-bottom: 0 !important;
  }

  :deep(.ant-descriptions-item) {
    padding-bottom: 8px !important;
  }

  :deep(.ant-descriptions-row:last-child .ant-descriptions-item) {
    padding-bottom: 0 !important;
  }
</style>
