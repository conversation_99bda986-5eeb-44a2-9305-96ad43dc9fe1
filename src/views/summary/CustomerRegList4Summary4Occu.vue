<template>
  <div>
    <!--简化的查询区域-->
    <div class="jeecg-basic-table-form-container compact-search">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" layout="inline">
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-item name="examNo" label="体检号" style="margin-bottom: 8px">
              <a-input
                allow-clear
                size="small"
                placeholder="体检号"
                v-model:value="queryParam.examNo"
                :class="{ 'scan-highlight-input': scanHighlight === queryParam.examNo }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="idCard" label="证件号" style="margin-bottom: 8px">
              <a-input allow-clear size="small" placeholder="证件号" v-model:value="queryParam.idCard" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-space size="small" style="width: 100%; justify-content: space-between">
              <a-button size="small" type="primary" @click="searchQuery" style="flex: 1">
                <SearchOutlined />
                查询
              </a-button>
              <a-button size="small" @click="searchReset" style="flex: 1">
                <ReloadOutlined />
                重置
              </a-button>
              <a-button size="small" @click="openAdvancedSearch" style="flex: 1">
                <FilterOutlined />
                高级
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="null" size="small">
      <!--插槽:table标题-->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex == 'initailSummaryMethod'">
          <j-dict-select-tag :value="text" dict-code="initail_summary_method" />
        </template>
        <template v-if="column.dataIndex == 'preSummaryMethod'">
          <j-dict-select-tag :value="text" dict-code="pre_summary_method" />
        </template>
        <template v-if="column.dataIndex == 'summaryStatus'">
          <a-tag :color="getSummaryStatusColor(text)">{{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'examNo'">
          <span :class="{ 'scan-highlight-text': scanHighlight && text?.includes(scanHighlight) }">
            {{ text }}
          </span>
        </template>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 高级搜索弹窗 -->
    <advanced-search-modal ref="advancedSearchModalRef" @search="handleAdvancedSearch" @reset="handleAdvancedReset" />
  </div>
</template>

<script lang="ts" name="customer-reg-list4-summary4-occu" setup>
  import { onMounted, reactive, ref, watch, nextTick } from 'vue';
  import { SearchOutlined, ReloadOutlined, FilterOutlined } from '@ant-design/icons-vue';
  import type { RangeValue } from '#/types';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns4Summary } from '/@/views/reg/CustomerReg.data';
  import { listReg, updateReportEditLockFlag } from '/@/views/summary/CustomerRegSummary.api.ts';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import { getRegById } from '@/views/summary/Summary.api';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import AdvancedSearchModal from './components/AdvancedSearchModal4Occu.vue';

  const userStore = useUserStore();
  const { token } = theme.useToken();
  const { createMessage } = useMessage();
  const addCurrentUser2QueryParma = ref<string>('0');

  const props = defineProps<{
    scanHighlight?: string;
  }>();

  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'reloadRecord']);

  const formRef = ref();
  const advancedSearchModalRef = ref();

  // 简化的查询参数，只保留基本条件
  const queryParam = reactive<any>({
    status: '已登记',
    sortOrder: '降序',
    dateType: '登记日期',
    doctorType: '指定的主检',
    examCatory: '职业病体检', // 固定为职业病体检
  });

  // 高级查询参数
  const advancedQueryParam = reactive<any>({});

  // 根据addCurrentUser2QueryParma判断是否添加当前用户到查询条件
  watch(
    () => addCurrentUser2QueryParma.value,
    (newVal) => {
      if (newVal == '1') {
        queryParam.doctor = userStore.getUserInfo.username ?? null;
      } else {
        queryParam.doctor = null;
      }
    }
  );

  // 日期范围处理
  const regDateRange = ref<RangeValue>([dayjs().subtract(7, 'day'), dayjs()]);

  watch(
    () => regDateRange.value,
    () => {
      searchQuery();
    }
  );

  /**表格相关操作*/
  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showTableSetting: false,
      showIndexColumn: true,
      api: listReg,
      columns: columns4Summary.filter((col) =>
        // 只显示职业检相关的列
        ['examNo', 'name', 'gender', 'age', 'companyName', 'riskFactor', 'summaryStatus', 'regTime'].includes(col.dataIndex)
      ),
      canResize: true,
      canColDrag: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      striped: true,
      actionColumn: {
        width: 60,
        fixed: 'right',
      },
      pagination: {
        pageSize: 20,
        simple: true, // 使用简单分页
      },
      customRow: (record) => {
        return {
          onDblclick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      beforeFetch: (params) => {
        // 合并基本查询参数和高级查询参数
        const mergedParams = Object.assign({}, params, queryParam, advancedQueryParam);

        // 处理日期范围
        if (regDateRange.value) {
          mergedParams.dateStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
          mergedParams.dateEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
        }
        mergedParams.examCatory = '职业病体检';
        return mergedParams;
      },
      afterFetch: (dataSource) => {
        if (currentRow.value && dataSource.length > 0) {
          let record = dataSource.find((item) => item.id === currentRow.value.id);
          if (record) {
            currentRow.value = record;
            emit('reloadRecord', record);
          }
        }
        return dataSource;
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
  });

  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }] = tableContext;

  // 总检状态颜色
  const getSummaryStatusColor = (status: string) => {
    const colorMap = {
      未总检: 'default',
      已预检: 'processing',
      已初检: 'warning',
      已主检: 'success',
      审核通过: 'success',
      驳回: 'error',
    };
    return colorMap[status] || 'default';
  };

  /**
   * 查询
   */
  async function searchQuery() {
    await reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    Object.keys(advancedQueryParam).forEach((key) => {
      delete advancedQueryParam[key];
    });
    reload();
  }

  /**
   * 打开高级搜索
   */
  function openAdvancedSearch() {
    advancedSearchModalRef.value?.open(advancedQueryParam);
  }

  /**
   * 处理高级搜索
   */
  function handleAdvancedSearch(params: any) {
    Object.assign(advancedQueryParam, params);
    searchQuery();
  }

  /**
   * 重置高级搜索
   */
  function handleAdvancedReset() {
    Object.keys(advancedQueryParam).forEach((key) => {
      delete advancedQueryParam[key];
    });
    searchQuery();
  }

  function reloadPage() {
    reload();
  }

  function reloadCurrent() {
    if (currentRow.value.id) {
      getRegById({ regId: currentRow.value.id }).then((record) => {
        Object.assign(currentRow.value, record);
      });
    }
  }

  /**
   * 扫码搜索方法
   */
  const searchByExamNo = async (examNo: string) => {
    // 更新查询参数
    queryParam.examNo = examNo;

    // 触发搜索
    await searchQuery();

    // 如果有结果，自动选中第一条
    const tableData = getDataSource();
    if (tableData && tableData.length > 0) {
      const firstRecord = tableData[0];
      currentRow.value = firstRecord;
      emit('rowClick', firstRecord);

      // 高亮匹配的行
      nextTick(() => {
        highlightMatchedRow(examNo);
      });
    } else {
      throw new Error('未找到匹配的体检记录');
    }
  };

  /**
   * 高亮匹配的行
   */
  const highlightMatchedRow = (examNo: string) => {
    const rows = document.querySelectorAll('.ant-table-tbody tr');
    rows.forEach((row) => {
      const examNoCell = row.querySelector('td:nth-child(2)'); // 体检号列
      if (examNoCell && examNoCell.textContent?.includes(examNo)) {
        row.classList.add('scan-highlight-row');
        // 3秒后移除高亮
        setTimeout(() => {
          row.classList.remove('scan-highlight-row');
        }, 3000);
      }
    });
  };

  /**
   * 操作栏
   */
  function getTableAction(record) {
    let action = [];
    if (record.reportEditLockFlag == '1') {
      action.push({
        label: '已锁',
        onClick: null,
      });
    } else {
      if (!(record.summaryStatus == '审核通过' || record.summaryStatus == '驳回')) {
        action.push({
          label: '锁定',
          onClick: handleEdit.bind(null, record),
        });
      }
    }
    return action;
  }

  function handleEdit(record: any) {
    currentRow.value = record;
    emit('rowClick', record);
  }

  onMounted(() => {
    const savedSortOrder = localStorage.getItem('summaryRegDateType');
    queryParam.dateType = savedSortOrder || '降序';

    querySysParamByCode({ code: 'summary_list_condition_current_user' }).then((res) => {
      addCurrentUser2QueryParma.value = res.result;
    });

    // 初始化搜索
    searchQuery();
  });

  watch(
    () => queryParam.dateType,
    (newSortOrder) => {
      localStorage.setItem('summaryRegDateType', newSortOrder);
    }
  );

  // 暴露方法给父组件
  defineExpose({
    searchQuery,
    reloadPage,
    reloadCurrent,
    searchByExamNo,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 8px;

    &.compact-search {
      .ant-form-item {
        margin-bottom: 4px;
      }

      .ant-form-item-label {
        font-size: 12px;
        padding-bottom: 2px;
      }
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 3px v-bind('token.colorPrimary');
  }

  :deep(.scan-highlight-row) {
    background-color: #fff2e8 !important;
    border: 1px solid #ff7a00 !important;
    animation: scanPulse 1s ease-in-out;
  }

  .scan-highlight-input {
    border-color: #ff7a00 !important;
    box-shadow: 0 0 0 2px rgba(255, 122, 0, 0.2) !important;
  }

  .scan-highlight-text {
    background-color: #fff2e8;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
    color: #ff7a00;
  }

  @keyframes scanPulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.01);
    }
    100% {
      transform: scale(1);
    }
  }

  :deep(.ant-table-small) {
    .ant-table-tbody > tr > td {
      padding: 4px 8px;
      font-size: 12px;
    }

    .ant-table-thead > tr > th {
      padding: 6px 8px;
      font-size: 12px;
    }
  }

  :deep(.ant-pagination-simple) {
    .ant-pagination-simple-pager {
      input {
        width: 40px;
      }
    }
  }
</style>
