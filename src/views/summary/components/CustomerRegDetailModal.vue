<template>
  <a-modal
    title="体检记录详情"
    :width="600"
    :open="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :okButtonProps="{ class: { 'jee-hidden': true } }"
    cancelText="关闭"
  >
    <div v-if="record" style="padding: 10px">
      <!-- 警告提示 -->
      <a-alert
        v-if="record.examCategory !== '职业病体检'"
        :message="`该记录为${record.examCategory}，无法在职业病体检总检页面处理`"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />

      <!-- 基本信息 -->
      <a-descriptions title="基本信息" :column="2" bordered size="small">
        <a-descriptions-item label="体检号">
          <a-tag color="blue">{{ record.examNo }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="姓名">
          <strong>{{ record.name }}</strong>
        </a-descriptions-item>
        <a-descriptions-item label="性别">
          <a-tag :color="record.gender === '男' ? 'blue' : 'pink'">
            {{ record.gender }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="年龄"> {{ record.age }}{{ record.ageUnit || '岁' }} </a-descriptions-item>
        <a-descriptions-item label="证件号" :span="2">
          {{ record.idCard }}
        </a-descriptions-item>
        <a-descriptions-item label="体检类型">
          <a-tag :color="getExamCategoryColor(record.examCategory)">
            {{ record.examCategory }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="总检状态">
          <a-tag :color="getSummaryStatusColor(record.summaryStatus)">
            {{ record.summaryStatus || '未总检' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="登记时间" :span="2">
          {{ record.regTime }}
        </a-descriptions-item>
        <a-descriptions-item label="单位名称" :span="2">
          {{ record.companyName }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 操作建议 -->
      <div style="margin-top: 16px">
        <a-card size="small" title="操作建议">
          <div v-if="record.examCategory === '职业病体检'">
            <a-space direction="vertical">
              <div>
                <CheckCircleOutlined style="color: #52c41a; margin-right: 8px" />
                该记录为职业病体检，可以在当前页面进行总检操作
              </div>
              <a-button type="primary" @click="handleSelectRecord"> 选择此记录进行总检 </a-button>
            </a-space>
          </div>
          <div v-else>
            <a-space direction="vertical">
              <div>
                <ExclamationCircleOutlined style="color: #faad14; margin-right: 8px" />
                该记录为{{ record.examCategory }}，请前往对应的总检页面进行处理
              </div>
              <a-space>
                <!--                <a-button type="primary" @click="navigateToCorrectPage">-->
                <!--                  前往{{ record.examCategory }}总检页面-->
                <!--                </a-button>-->
                <a-button @click="handleCancel"> 关闭 </a-button>
              </a-space>
            </a-space>
          </div>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, defineExpose } from 'vue';
  import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { ICustomerReg } from '#/types';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const visible = ref<boolean>(false);
  const record = ref<ICustomerReg | null>(null);

  const emit = defineEmits(['select', 'cancel']);

  // 获取体检类型颜色
  function getExamCategoryColor(category: string): string {
    const colorMap: Record<string, string> = {
      职业病体检: 'orange',
      健康体检: 'green',
      入职体检: 'blue',
      离职体检: 'purple',
      年度体检: 'cyan',
    };
    return colorMap[category] || 'default';
  }

  // 获取总检状态颜色
  function getSummaryStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      未总检: 'default',
      初检完成: 'processing',
      总检完成: 'success',
      审核通过: 'success',
      驳回: 'error',
    };
    return colorMap[status] || 'default';
  }

  /**
   * 打开模态框
   * @param customerReg 体检记录
   */
  function open(customerReg: ICustomerReg) {
    record.value = customerReg;
    visible.value = true;
  }

  /**
   * 选择记录进行总检
   */
  function handleSelectRecord() {
    if (record.value) {
      emit('select', record.value);
      handleCancel();
    }
  }

  /**
   * 导航到正确的总检页面
   */
  function navigateToCorrectPage() {
    if (!record.value) return;

    const examCategory = record.value.examCategory;
    let routePath = '';

    switch (examCategory) {
      case '健康体检':
        routePath = '/summary/health';
        break;
      case '入职体检':
        routePath = '/summary/entry';
        break;
      case '离职体检':
        routePath = '/summary/exit';
        break;
      case '年度体检':
        routePath = '/summary/annual';
        break;
      default:
        routePath = '/summary/general';
        break;
    }

    // 携带体检号参数跳转
    router.push({
      path: routePath,
      query: {
        examNo: record.value.examNo,
      },
    });

    message.success(`正在跳转到${examCategory}总检页面`);
    handleCancel();
  }

  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    handleCancel();
  }

  /**
   * 取消按钮点击事件
   */
  function handleCancel() {
    visible.value = false;
    record.value = null;
    emit('cancel');
  }

  defineExpose({
    open,
  });
</script>

<style scoped>
  :deep(.ant-descriptions-item-label) {
    font-weight: 500;
  }

  :deep(.ant-descriptions-item-content) {
    color: #262626;
  }
</style>
