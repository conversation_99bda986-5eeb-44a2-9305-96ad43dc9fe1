<template>
  <a-modal v-model:open="visible" title="高级搜索条件" width="600px" @ok="handleOk" @cancel="handleCancel">
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
    <div style="padding: 10px">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="examNo" label="体检号">
              <a-input allow-clear placeholder="请输入体检号" v-model:value="formData.examNo" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="name" label="姓名">
              <a-input allow-clear placeholder="请输入姓名" v-model:value="formData.name" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="idCard" label="证件号">
              <a-input allow-clear placeholder="请输入证件号" v-model:value="formData.idCard" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="summaryStatus" label="总检状态">
              <j-dict-select-tag dict-code="summary_status" placeholder="总检状态" v-model:value="formData.summaryStatus" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="companyId" label="所属单位">
              <j-async-search-select
                placeholder="请选择单位"
                v-model:value="formData.companyId"
                dict="company where del_flag=0,name,id"
                :allow-clear="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="companyRegId" label="所属预约">
              <j-async-search-select
                placeholder="请选择预约"
                v-model:value="formData.companyRegId"
                dict="company_reg,reg_name,id"
                :allow-clear="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="riskFactor" label="危害因素">
              <j-dict-select-tag dict-code="risk_factor" placeholder="危害因素" v-model:value="formData.riskFactor" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="workType" label="工种">
              <j-dict-select-tag dict-code="work_type" placeholder="工种" v-model:value="formData.workType" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="doctor" label="主检医生">
              <j-select-user placeholder="请选择主检医生" v-model:value="formData.doctor" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="preSummaryMethod" label="预检方式">
              <j-dict-select-tag dict-code="pre_summary_method" placeholder="预检方式" v-model:value="formData.preSummaryMethod" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="initailSummaryMethod" label="初检方式">
              <j-dict-select-tag dict-code="initail_summary_method" placeholder="初检方式" v-model:value="formData.initailSummaryMethod" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="daysFromReg" label="积案天数">
              <a-input-number v-model:value="formData.daysFromReg" placeholder="积案天数" :min="0" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item name="dateRange" label="日期范围">
              <a-range-picker v-model:value="formData.dateRange" style="width: 100%" :presets="rangePresets" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="dateType" label="日期类型">
              <a-select v-model:value="formData.dateType" placeholder="日期类型">
                <a-select-option value="登记日期">登记日期</a-select-option>
                <a-select-option value="体检日期">体检日期</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="sortOrder" label="排序">
              <a-select v-model:value="formData.sortOrder" placeholder="排序方式">
                <a-select-option value="升序">升序</a-select-option>
                <a-select-option value="降序">降序</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import dayjs from 'dayjs';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JSelectUser from '@/components/Form/src/jeecg/components/JSelectUser.vue';

  const emit = defineEmits(['search', 'reset']);

  const visible = ref(false);
  const formRef = ref();

  const formData = reactive<any>({
    dateType: '登记日期',
    sortOrder: '降序',
  });

  // 日期预设
  const rangePresets = [
    { label: '今天', value: [dayjs(), dayjs()] },
    { label: '昨天', value: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] },
    { label: '最近3天', value: [dayjs().subtract(2, 'day'), dayjs()] },
    { label: '最近7天', value: [dayjs().subtract(6, 'day'), dayjs()] },
    { label: '最近30天', value: [dayjs().subtract(29, 'day'), dayjs()] },
    { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
    { label: '上月', value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
  ];

  const open = (initialData?: any) => {
    visible.value = true;
    console.log('高级搜索弹窗接收到的参数:', initialData);
    console.log('当前formData:', formData);

    if (initialData) {
      Object.assign(formData, initialData);
      console.log('合并后的formData:', formData);
    }
  };

  const handleOk = () => {
    const searchParams = { ...formData };

    // 处理日期范围
    if (searchParams.dateRange && searchParams.dateRange.length === 2) {
      searchParams.dateStart = searchParams.dateRange[0].format('YYYY-MM-DD') + ' 00:00:00';
      searchParams.dateEnd = searchParams.dateRange[1].format('YYYY-MM-DD') + ' 23:59:59';
      delete searchParams.dateRange;
    }

    emit('search', searchParams);
    visible.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleReset = () => {
    formRef.value?.resetFields();
    Object.keys(formData).forEach((key) => {
      if (key !== 'dateType' && key !== 'sortOrder') {
        delete formData[key];
      }
    });
    formData.dateType = '登记日期';
    formData.sortOrder = '降序';

    emit('reset');
    visible.value = false;
  };

  defineExpose({
    open,
  });
</script>

<style scoped>
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
</style>
